require('dotenv').config(); // Add this at the top

// const functions = require("firebase-functions");
const { onRequest } = require("firebase-functions/v2/https");
var admin = require("firebase-admin");
var serviceAccount = require("./config/firebase-service-account.json");
 
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: process.env.STORAGE_BUCKET || 'kissandost-9570f.firebasestorage.app' // Replace with your actual bucket
});
const firestore = admin.firestore();
const storage = admin.storage();
// var db = admin.firestore();
const express = require('express');
const cors = require('cors');
const { OpenAI } = require('openai');
// const admin = require('firebase-admin');
// const { v4: uuidv4 } = require('uuid');


const app = express();

// Middleware
app.use(cors());



// Lazy route mounting
let routesInitialized = false;
function initRoutes() {
    if (!routesInitialized) {
        console.log("🔁 Initializing routes...");
        require('./routes/openAi')(app);
        routesInitialized = true;
        console.log("🔁 Routes Initialized Successfully...");
    }
}
 
// Middleware to ensure routes are initialized on first request
app.use((req, res, next) => {
    initRoutes();
    next();
});
// app.use(express.json({ limit: '50mb' }));
// app.use(express.urlencoded({ limit: '50mb', extended: true }));

// Initialize Firebase Admin
// const serviceAccount = require('./firebase-service-account.json'); // You'll need to add this file

// admin.initializeApp({
//   credential: admin.credential.cert(serviceAccount),
//   storageBucket: process.env.STORAGE_BUCKET || 'kissandost-9570f.firebasestorage.app' // Replace with your actual bucket
// });

// const firestore = admin.firestore();
// const storage = admin.storage();

// Initialize service modules with Firebase instances
// setFirestore(firestore);
// setStorage(storage);




// app.listen(port, () => {
//   console.log(`MCP Server running on http://localhost:${port}`);
//   console.log('Firebase Admin initialized');
//   console.log('OpenAI client initialized');
// });
// exports.animalApp = functions.region("australia-southeast1").https.onRequest(app);
 exports.animalApp = onRequest(
    {
        region: "australia-southeast1"
    },
    app
);