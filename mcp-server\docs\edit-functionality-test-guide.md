# Edit Functionality Test Guide

## Overview
This document provides comprehensive testing scenarios for the newly implemented edit functionality in the MCP server. The edit functionality supports farms, animals, expenses, health records, tasks, and employees with role-based permissions and multilingual support.

## Implemented Features

### ✅ Completed Edit Functionalities

1. **Farm Edit** - Fully implemented
   - Natural language processing for edit requests
   - Farm selection interface with visual display
   - Field-specific updates (name, location, type, size, description)
   - Role-based permissions (owner/admin only)
   - Multilingual support (English/Urdu)

2. **Animal Edit** - Fully implemented
   - Natural language processing for edit requests
   - Animal selection interface with visual display
   - Field-specific updates (name, species, breed, weight, color, etc.)
   - Role-based permissions (owner/admin only)
   - Multilingual support (English/Urdu)

3. **Expense Edit** - Fully implemented
   - Natural language processing for edit requests
   - Expense selection interface
   - Field-specific updates (amount, category, description, payment method)
   - Role-based permissions (owner/admin only)
   - Multilingual support (English/Urdu)

4. **Health Record Edit** - Placeholder implemented
   - Basic structure in place
   - Returns "coming soon" message
   - Role-based permissions implemented

5. **Task Edit** - Placeholder implemented
   - Basic structure in place
   - Returns "coming soon" message
   - Role-based permissions implemented

6. **Employee Edit** - Placeholder implemented
   - Basic structure in place
   - Returns "coming soon" message
   - Owner-only permissions implemented

## Test Scenarios

### 1. Farm Edit Testing

#### English Commands:
```
- "Edit Haven View Farm"
- "Edit farm" (shows selection list)
- "Change name to Green Valley Farm"
- "Change location to Main Street, Lahore"
- "Change type to dairy farm"
- "Change size to 200 acres"
```

#### Urdu Commands:
```
- "Haven View Farm ایڈٹ کریں"
- "فارم ایڈٹ کریں" (shows selection list)
- "نام تبدیل کرو Green Valley Farm"
- "پتہ تبدیل کرو Main Street, Lahore"
- "قسم تبدیل کرو dairy farm"
- "سائز تبدیل کرو 200 acres"
```

#### Expected Flow:
1. User says "Edit farm"
2. System shows visual farm selection interface
3. User selects a farm
4. System shows current farm details and edit options
5. User specifies field to change
6. System updates the field and confirms

### 2. Animal Edit Testing

#### English Commands:
```
- "Edit Bella" (specific animal name)
- "Edit animal" (shows selection list)
- "Change name to Luna"
- "Change breed to Holstein"
- "Change weight to 450 kg"
- "Change color to Brown and White"
```

#### Urdu Commands:
```
- "Bella ایڈٹ کریں"
- "جانور ایڈٹ کریں"
- "نام تبدیل کرو Luna"
- "نسل تبدیل کرو Holstein"
- "وزن تبدیل کرو 450 kg"
- "رنگ تبدیل کرو Brown and White"
```

### 3. Expense Edit Testing

#### English Commands:
```
- "Edit expense" (shows selection list)
- "Change amount to 5000"
- "Change category to Feed"
- "Change description to Cattle feed purchase"
- "Change payment to Cash"
```

#### Urdu Commands:
```
- "خرچہ ایڈٹ کریں"
- "رقم تبدیل کرو 5000"
- "کیٹگری تبدیل کرو Feed"
- "تفصیل تبدیل کرو Cattle feed purchase"
- "ادائیگی تبدیل کرو Cash"
```

## Permission Testing

### Owner Role:
- ✅ Can edit all farms
- ✅ Can edit all animals
- ✅ Can edit all expenses
- ✅ Can edit employees (when implemented)

### Admin Role:
- ✅ Can edit assigned farms only
- ✅ Can edit animals in assigned farms
- ✅ Can edit expenses in assigned farms
- ❌ Cannot edit employees

### Caretaker Role:
- ❌ Cannot edit farms
- ❌ Cannot edit animals
- ❌ Cannot edit expenses
- ❌ Cannot edit employees

## Database Functions Implemented

### Update Functions:
- `updateFarmInDatabase(farmId, updateData, userId)`
- `updateAnimalInDatabase(animalId, farmId, updateData, userId)`
- `updateExpenseInDatabase(expenseId, farmId, updateData, userId)`
- `updateHealthRecordInDatabase(recordId, farmId, updateData, userId)`
- `updateTaskInDatabase(taskId, farmId, updateData, userId)`
- `updateEmployeeInDatabase(employeeId, updateData, userId)`

### Features:
- Automatic timestamp updates
- Activity logging
- Data validation
- Error handling
- Clean data processing (removes undefined values)

## Request Analysis Patterns

### Edit Detection:
- English: "edit", "update", "modify", "change"
- Urdu: "ایڈٹ", "تبدیل", "اپڈیٹ"

### Entity Recognition:
- Farm: "farm", "فارم"
- Animal: "animal", "cow", "goat", etc., "جانور", "گائے", "بکری"
- Expense: "expense", "cost", "bill", "خرچہ", "لاگت", "بل"

## Error Handling

### Common Error Scenarios:
1. **Permission Denied**: User role doesn't allow editing
2. **Entity Not Found**: Specified farm/animal/expense doesn't exist
3. **Invalid Field**: Trying to edit non-existent or restricted field
4. **Parse Error**: Cannot understand the edit request
5. **Database Error**: Failed to update in database

### Error Messages:
- Available in both English and Urdu
- Clear and actionable
- Includes suggestions for correct usage

## Next Steps for Full Implementation

### Health Record Edit (TODO):
- Implement selection interface
- Add field-specific update logic
- Test with various record types

### Task Edit (TODO):
- Implement selection interface
- Add field-specific update logic
- Handle task assignment changes

### Employee Edit (TODO):
- Implement selection interface
- Add field-specific update logic
- Exclude email/password from editable fields
- Add proper validation

## Testing Checklist

- [ ] Test all farm edit scenarios
- [ ] Test all animal edit scenarios  
- [ ] Test all expense edit scenarios
- [ ] Test permission restrictions for each role
- [ ] Test multilingual support (English/Urdu)
- [ ] Test error handling scenarios
- [ ] Test visual selection interfaces
- [ ] Test database updates and activity logging
- [ ] Test edge cases (empty data, invalid inputs)
- [ ] Test concurrent edit operations

## API Endpoints

### Main Endpoint:
- `POST /open-ai-chat`

### Request Format:
```json
{
  "prompt": "Edit farm",
  "language": "en",
  "userId": "user123",
  "context": {
    "action": "edit_farm",
    "selectedFarm": {...}
  }
}
```

### Response Format:
```json
{
  "message": "Farm updated successfully",
  "farmData": {...},
  "updated": true,
  "databaseId": "farm123"
}
```
