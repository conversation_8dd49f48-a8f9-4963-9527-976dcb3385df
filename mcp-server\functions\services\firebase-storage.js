const { v4: uuidv4 } = require('uuid');
var admin = require("firebase-admin");

// Firebase instances will be passed from the main app
let storage = admin.storage();

// const setStorage = (storageInstance) => {
//   storage = storageInstance;
// };

const uploadImageToFirebase = async (imageUri, path) => {
  try {
    console.log('Uploading image to Firebase Storage...');

    // Convert image URI to buffer
    let imageBuffer;
    if (imageUri.startsWith('data:image')) {
      // Handle base64 images
      const base64Data = imageUri.split(',')[1];
      imageBuffer = Buffer.from(base64Data, 'base64');
    } else {
      // Handle file URIs by fetching them
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(imageUri);
      const arrayBuffer = await response.arrayBuffer();
      imageBuffer = Buffer.from(arrayBuffer);
    }

    const bucket = storage.bucket();
    const fileName = `${path}/${uuidv4()}.jpg`;
    const file = bucket.file(fileName);

    await file.save(imageBuffer, {
      metadata: {
        contentType: 'image/jpeg',
        metadata: {
          'uploaded-by': 'mcp-server',
          'timestamp': Date.now().toString()
        }
      }
    });

    // Make the file publicly accessible
    await file.makePublic();

    // Get the public URL
    const publicUrl = `https://storage.googleapis.com/${bucket.name}/${fileName}`;
    console.log('Image uploaded successfully:', publicUrl);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading image to Firebase:', error);
    throw error;
  }
};

const uploadBase64ImageToStorage = async (base64Data, userId, folder = 'farms') => {
  try {
    // Remove data:image/jpeg;base64, prefix if present
    const base64String = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');

    // Convert base64 to buffer
    const buffer = Buffer.from(base64String, 'base64');

    // Create a unique filename
    const timestamp = Date.now();
    const filename = `${folder}/${userId}/${timestamp}.jpg`;

    const bucket = storage.bucket();
    const file = bucket.file(filename);

    await file.save(buffer, {
      metadata: {
        contentType: 'image/jpeg',
        metadata: {
          'uploaded-by': 'mcp-server',
          'timestamp': timestamp.toString()
        }
      }
    });

    // Make the file publicly accessible
    await file.makePublic();

    // Get the public URL
    const publicUrl = `https://storage.googleapis.com/${bucket.name}/${filename}`;
    console.log('Base64 image uploaded successfully:', publicUrl);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading base64 image to Firebase:', error);
    throw error;
  }
};

module.exports = {
  // setStorage,
  uploadImageToFirebase,
  uploadBase64ImageToStorage
};
