[debug] [2025-07-25T11:59:36.162Z] ----------------------------------------------------------------------
[debug] [2025-07-25T11:59:36.174Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-07-25T11:59:36.175Z] CLI Version:   14.11.1
[debug] [2025-07-25T11:59:36.176Z] Platform:      win32
[debug] [2025-07-25T11:59:36.176Z] Node Version:  v22.17.0
[debug] [2025-07-25T11:59:36.177Z] Time:          Fri Jul 25 2025 16:59:36 GMT+0500 (Pakistan Standard Time)
[debug] [2025-07-25T11:59:36.178Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-25T11:59:37.390Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-25T11:59:37.392Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-25T11:59:37.436Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-25T11:59:37.437Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-25T11:59:37.460Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-kissandost-9570f.json
[debug] [2025-07-25T11:59:37.533Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-25T11:59:37.534Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-25T11:59:37.534Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-25T11:59:37.535Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-25T11:59:37.609Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json
[debug] [2025-07-25T11:59:37.620Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\malikeahtesham111_gmail_com_application_default_credentials.json"}}
[debug] [2025-07-25T11:59:37.625Z] Checked if tokens are valid: true, expires at: 1753447087123
[debug] [2025-07-25T11:59:37.625Z] Checked if tokens are valid: true, expires at: 1753447087123
[debug] [2025-07-25T11:59:37.634Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig [none]
[debug] [2025-07-25T11:59:38.377Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig 200
[debug] [2025-07-25T11:59:38.379Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig {"projectId":"kissandost-9570f","databaseURL":"https://kissandost-9570f-default-rtdb.firebaseio.com","storageBucket":"kissandost-9570f.firebasestorage.app"}
[info] i  functions: Watching "D:\Sherry\projects\apps\mcp-server\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"D:\\Sherry\\projects\\apps\\mcp-server\\functions\" for Cloud Functions..."}}
[debug] [2025-07-25T11:59:38.609Z] Validating nodejs source
[debug] [2025-07-25T11:59:44.189Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T11:59:44.190Z] Building nodejs source
[debug] [2025-07-25T11:59:44.191Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T11:59:44.211Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T11:59:44.238Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
